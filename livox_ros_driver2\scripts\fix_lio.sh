#!/bin/bash

echo "=== Livox LIO 修复脚本 ==="
echo "时间: $(date)"
echo ""

# 创建备份目录
BACKUP_DIR="../config/backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "1. 备份当前配置..."
cp ../config/MID360_config.json "$BACKUP_DIR/"
echo "✓ 配置已备份到: $BACKUP_DIR"

echo ""
echo "2. 停止当前驱动进程..."
pkill -f "livox_ros_driver2_node"
sleep 2

echo ""
echo "3. 清理ROS环境..."
if command -v rosnode &> /dev/null; then
    rosnode cleanup
fi

echo ""
echo "4. 重置网络配置..."
# 检查并修复网络配置
cat > ../config/MID360_config_fixed.json << 'EOF'
{
  "lidar_summary_info" : {
    "lidar_type": 8
  },
  "MID360": {
    "lidar_net_info" : {
      "cmd_data_port": 56100,
      "push_msg_port": 56200,
      "point_data_port": 56300,
      "imu_data_port": 56400,
      "log_data_port": 56500
    },
    "host_net_info" : {
      "cmd_data_ip" : "************",
      "cmd_data_port": 56101,
      "push_msg_ip": "************",
      "push_msg_port": 56201,
      "point_data_ip": "************",
      "point_data_port": 56301,
      "imu_data_ip" : "************",
      "imu_data_port": 56401,
      "log_data_ip" : "************",
      "log_data_port": 56501
    }
  },
  "lidar_configs" : [
    {
      "ip" : "*************",
      "pcl_data_type" : 1,
      "pattern_mode" : 0,
      "extrinsic_parameter" : {
        "roll": 0.0,
        "pitch": 0.0,
        "yaw": 0.0,
        "x": 0,
        "y": 0,
        "z": 0
      }
    }
  ]
}
EOF

echo "✓ 已创建修复后的配置文件"

echo ""
echo "5. 应用修复配置..."
cp ../config/MID360_config_fixed.json ../config/MID360_config.json
echo "✓ 配置文件已更新"

echo ""
echo "6. 重启网络接口..."
sudo ip link set eth0 down 2>/dev/null
sudo ip link set eth0 up 2>/dev/null
sleep 2

echo ""
echo "7. 测试网络连接..."
ping -c 3 ************* > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ 雷达网络连接恢复正常"
else
    echo "✗ 雷达网络连接仍有问题，请检查硬件连接"
fi

echo ""
echo "=== 修复完成 ==="
echo ""
echo "下一步操作:"
echo "1. 重新启动 livox 驱动:"
echo "   roslaunch livox_ros_driver2 rviz_MID360.launch"
echo ""
echo "2. 如果问题仍然存在，尝试:"
echo "   - 重启整个系统"
echo "   - 检查雷达硬件连接"
echo "   - 重新标定雷达"
echo ""
echo "3. 监控LIO性能:"
echo "   rostopic echo /your_lio_topic"
