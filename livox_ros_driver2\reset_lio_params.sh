#!/bin/bash

echo "=== LIO 参数重置脚本 ==="
echo "时间: $(date)"
echo ""

# 常见LIO算法的参数重置
echo "1. 重置ROS参数服务器..."
rosparam delete /fast_lio 2>/dev/null || true
rosparam delete /lio_sam 2>/dev/null || true
rosparam delete /lio_mapping 2>/dev/null || true

echo "2. 清理TF缓存..."
rosservice call /tf2_buffer_server/clear_tf_buffer 2>/dev/null || true

echo "3. 重置里程计..."
rostopic pub /initialpose geometry_msgs/PoseWithCovarianceStamped "
header:
  seq: 0
  stamp:
    secs: 0
    nsecs: 0
  frame_id: 'map'
pose:
  pose:
    position:
      x: 0.0
      y: 0.0
      z: 0.0
    orientation:
      x: 0.0
      y: 0.0
      z: 0.0
      w: 1.0
  covariance: [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
" --once 2>/dev/null || true

echo ""
echo "=== 常见LIO问题解决方案 ==="
echo ""
echo "如果残差过高 (>0.04)，尝试以下方法："
echo ""
echo "1. 【立即生效】重启LIO算法："
echo "   pkill -f 'your_lio_node'"
echo "   roslaunch your_lio_package your_lio_launch.launch"
echo ""
echo "2. 【参数调整】降低ICP收敛阈值："
echo "   - max_iterations: 20 -> 30"
echo "   - transformation_epsilon: 0.01 -> 0.005"
echo "   - euclidean_fitness_epsilon: 0.1 -> 0.05"
echo ""
echo "3. 【特征提取】调整特征点参数："
echo "   - edge_threshold: 0.1 -> 0.05"
echo "   - surf_threshold: 0.1 -> 0.05"
echo "   - feature_region: 增加分区数量"
echo ""
echo "4. 【环境因素】检查："
echo "   - 确保雷达周围没有移动物体"
echo "   - 检查是否有强光干扰"
echo "   - 确认雷达安装牢固"
echo ""
echo "5. 【系统重置】最后手段："
echo "   - 删除地图文件重新建图"
echo "   - 重启整个系统"
echo "   - 重新标定外参"

echo ""
echo "=== 脚本执行完成 ==="
