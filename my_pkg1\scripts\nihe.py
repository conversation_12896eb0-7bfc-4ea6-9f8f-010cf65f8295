import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score

# 设置中文字体，解决中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei']  # 黑体
plt.rcParams['axes.unicode_minus'] = False   # 负号正常显示

# 原始数据
x = np.array([3.587, 3.611, 3.667, 3.704, 3.768, 3.817, 3.857, 3.938])
y = np.array([61.927887, 61.418342, 61.74596, 61.599334, 59.616587, 58.373806, 58.124722, 59.19528])

# 普通二次多项式拟合
degree = 10# 二次多项式
coefficients = np.polyfit(x, y, degree)
poly_func = np.poly1d(coefficients)

# 打印拟合曲线方程
print("拟合曲线方程:")
print(poly_func)

# 拟合计算
x_new = np.linspace(np.min(x), np.max(x), 500)
y_new = poly_func(x_new)

# 计算拟合优度R²
y_fitted = poly_func(x)
r2 = r2_score(y, y_fitted)
print("R² 拟合优度（二次多项式拟合）:", r2)

# 绘图
plt.scatter(x, y, label='原始数据', color='blue')
plt.plot(x_new, y_new, label='二次多项式拟合曲线', color='red')

plt.xlabel('投影距离')
plt.ylabel('发射角度')
plt.legend()
plt.title('二次多项式拟合示例')
plt.show()