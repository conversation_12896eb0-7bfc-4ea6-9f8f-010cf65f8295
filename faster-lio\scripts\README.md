# Faster-LIO MID-360 建图与导航系统

## 📋 系统概述

本系统基于Faster-LIO，专门为Livox MID-360激光雷达设计，提供完整的建图、定位和导航功能。

### 🎯 主要功能
- **实时建图**：使用MID-360进行高精度SLAM建图
- **篮筐标记**：在建图过程中标记篮筐位置
- **自动重定位**：基于点云匹配的智能重定位
- **角度计算**：实时计算机器人与篮筐的角度和距离关系

## 🚀 快速开始

### 环境要求
```bash
# ROS依赖
sudo apt install ros-noetic-desktop-full

# Python依赖
pip install open3d numpy

# 编译faster_lio
cd ~/faster_lio
catkin_make
source devel/setup.bash
```

### 硬件要求
- Livox MID-360 激光雷达
- IMU传感器
- 运行ROS的计算机

### ⚡ 系统使用流程

#### 🗺️ 建图阶段（一次性）
```bash
# 1. 启动雷达 + faster-lio建图模式
roslaunch faster_lio mapping_mid360.launch rviz:=true

# 2. 行走建图，移动到篮筐正下方

# 3. 标记篮筐（新终端）
./src/faster-lio/scripts/start_mapping.sh  # 选择3

# 4. 建图完成按Ctrl+C中断（自动保存建图）
```

**重要：** 建图阶段必须使用 `mapping_mid360.launch`，它会保存地图文件

#### 🧭 导航阶段（每次使用）
**前提：机器人从建图原点出发**

```bash
# 1. 启动雷达 + faster-lio定位模式（不更新地图）
roslaunch faster_lio localization_mid360.launch rviz:=true

# 2. 加载保存的地图 + 确定机器人和篮筐位置
./src/faster-lio/scripts/start_navigation.sh  # 选择1

# 3. 计算并发布角度距离数据
./src/faster-lio/scripts/start_navigation.sh  # 选择2
```

**重要：** 导航阶段必须使用 `localization_mid360.launch`，它不会更新地图

**发布的话题：**
- `/y_angle` - 篮筐连线与雷达y轴正方向夹角（度）
- `/x_distance` - 雷达与篮筐x轴差值（米）
- `/xy_distance` - 雷达与篮筐2D投影距离（米）

#### 🔍 系统测试
```bash
# 测试系统是否正常工作
python3 ~/faster_lio/src/faster-lio/scripts/test_system.py
```

**测试内容：**
- faster_lio位置数据 (`/Odometry`)
- faster_lio点云数据 (`/cloud_registered`)
- 篮筐定位数据 (`/y_angle`, `/xy_distance`, `/x_distance`)

## 📖 完整使用流程

### 阶段一：建图 🗺️

#### 1. 启动建图系统
```bash
cd ~/faster_lio
./src/faster-lio/scripts/start_mapping.sh
```

选择选项：
- **选项1**：开始建图（带rviz）- 推荐，可视化建图过程
- **选项2**：开始建图（不带rviz）- 节省资源
- **选项3**：标记篮筐位置 - 在建图过程中使用

#### 2. 建图操作
1. 启动建图后，携带设备在环境中移动
2. 在rviz中观察建图效果
3. 建图完成后按 `Ctrl+C` 保存地图

#### 3. 标记篮筐位置（可选）
在建图过程中，打开新终端：
```bash
./src/faster-lio/scripts/start_mapping.sh
# 选择选项3：标记篮筐位置
```

操作说明：
- **空格键**：在当前位置标记篮筐
- **'l'键**：列出所有标记点
- **'c'键**：清除所有标记点
- **'q'键**：退出并保存

#### 4. 保存的文件
建图完成后，系统自动保存：
- `src/faster-lio/PCD/scans.pcd` - 点云地图
- `src/faster-lio/Log/traj.txt` - 运动轨迹
- `marked_points/marked_points_YYYYMMDD_HHMMSS.json` - 篮筐标记点

### 阶段二：导航定位 🧭

**前提条件：** 机器人从建图原点出发

#### 步骤1：启动faster_lio定位模式
```bash
roslaunch faster_lio localization_mid360.launch rviz:=true
```

**说明：** 使用定位模式launch文件，不会更新或保存地图

#### 步骤2：加载地图和篮筐标记点
```bash
./src/faster-lio/scripts/start_navigation.sh
# 选择选项1：启动定位系统（加载地图）
```

**功能：**
- 加载保存的点云地图
- 加载篮筐标记点
- 在rviz中显示地图和篮筐位置
- 确定机器人在地图中的位置（原点）
- 确定篮筐在地图中的位置

#### 步骤3：启动篮筐定位计算
```bash
./src/faster-lio/scripts/start_navigation.sh
# 选择选项2：启动篮筐定位（计算角度距离）
```

**功能：**
- 实时获取机器人当前位置
- 计算与篮筐的空间关系
- 发布角度和距离数据

#### 🎮 导航系统选项说明

| 选项 | 功能 | 说明 |
|------|------|------|
| 1 | 启动定位系统（加载地图） | 加载地图和篮筐标记点 |
| 2 | 启动篮筐定位（计算角度距离） | 实时计算发布数据 |
| 3-6 | 重定位功能 | 暂不使用 |

系统会发布以下话题：
- `/y_angle` (Float64) - 与篮筐连线和y轴正方向夹角（度）
- `/xy_distance` (Float64) - 与篮筐2D投影距离（米）
- `/x_distance` (Float64) - 与篮筐x轴差值（米）

## 📁 文件结构

```
faster_lio/
├── src/faster-lio/
│   ├── PCD/
│   │   └── scans.pcd                    # 点云地图
│   ├── Log/
│   │   └── traj.txt                     # 运动轨迹
│   └── scripts/
│       ├── README.md                    # 本文档
│       ├── start_mapping.sh            # 建图启动脚本
│       ├── start_navigation.sh         # 导航启动脚本
│       ├── mark_point.py              # 篮筐标记脚本
│       ├── load_map_with_points.py    # 地图加载脚本
│       ├── localization_with_basket.py # 篮筐定位脚本
│       ├── standalone_relocalize.py   # 独立重定位脚本（推荐）
│       ├── auto_relocalize.py         # 在线自动重定位脚本
│       ├── precise_relocalize.py      # 精确重定位脚本
│       └── relocalize.py              # 手动重定位脚本
└── marked_points/
    ├── marked_points_YYYYMMDD_HHMMSS.json  # 标记点（JSON格式）
    └── marked_points_YYYYMMDD_HHMMSS.txt   # 标记点（文本格式）
```

## 🔧 脚本详细说明

### 建图相关脚本

#### `start_mapping.sh`
建图系统启动脚本
- 选项1/2：启动faster_lio建图
- 选项3：启动篮筐标记器

#### `mark_point.py`
篮筐位置标记脚本
```python
# 主要功能
- 实时获取机器人位置
- 按空格键标记篮筐位置
- 保存标记点到JSON文件
- 在rviz中可视化标记点
```

### 导航相关脚本

#### `start_navigation.sh`
导航系统启动脚本
- 选项1：启动定位系统（加载地图）
- 选项2：启动篮筐定位（计算角度距离）
- 选项3：独立重定位（推荐 - 不依赖建图模式）
- 选项4：自动重定位（需要faster_lio运行）
- 选项5：精确重定位（在标记点位置时使用）
- 选项6：手动重定位

#### `load_map_with_points.py`
地图和标记点加载脚本
```python
# 主要功能
- 加载PCD点云地图
- 加载篮筐标记点
- 发布到ROS话题供rviz显示
```

#### `standalone_relocalize.py`
独立重定位脚本（推荐）⭐
```python
# 主要功能
- 不依赖faster_lio建图模式
- 直接使用原始激光雷达数据
- 与静态保存地图进行匹配
- 全局配准 + 精细配准算法
- 收集多帧数据提高精度
- 适用于任意位置重定位
```

#### `auto_relocalize.py`
在线自动重定位脚本
```python
# 主要功能
- 需要faster_lio系统运行
- 获取处理后的点云数据
- 使用ICP算法进行点云匹配
- 多假设匹配提高成功率
- 可能受建图模式干扰
```

#### `precise_relocalize.py`
精确重定位脚本
```python
# 主要功能
- 适用于已知标记点位置
- 直接使用标记点精确坐标
- 1cm位置精度，1°角度精度
- 交互式选择标记点
- 无需点云匹配
```

#### `relocalize.py`
手动重定位脚本
```python
# 主要功能
- 交互式输入位置坐标
- 支持命令行参数
- 发布初始位置到/initialpose话题
```

#### `localization_with_basket.py`
篮筐定位计算脚本
```python
# 主要功能
- 加载地图和篮筐标记点
- 实时接收机器人位置
- 计算与篮筐的角度和距离关系
- 发布计算结果到指定话题
```

## 📊 话题说明

### 输入话题
- `/livox/lidar` - MID-360点云数据
- `/livox/imu` - IMU数据
- `/Odometry` - 机器人位置信息

### 输出话题
- `/loaded_map` - 加载的点云地图
- `/basket_markers` - 篮筐标记可视化
- `/y_angle` - 与篮筐夹角（度）
- `/xy_distance` - 2D投影距离（米）
- `/x_distance` - X轴差值（米）
- `/initialpose` - 重定位位置

## 🎯 使用技巧

### 建图技巧
1. **移动速度**：保持匀速慢速移动，避免急转弯
2. **环境特征**：选择特征丰富的环境进行建图
3. **标记时机**：在篮筐正下方时标记，确保位置准确
4. **标记高度**：系统会在雷达正上方1.63米处显示篮筐标记

### 重定位技巧
1. **自动重定位**：优先使用，成功率高
2. **位置选择**：在特征明显的位置进行重定位
3. **验证结果**：在rviz中检查重定位是否正确

### 定位精度优化
1. **参数调整**：根据环境调整配置文件参数
2. **标记精度**：确保篮筐标记位置准确
3. **环境稳定**：避免动态物体干扰

## ⚠️ 常见问题

### Q1: 建图时rviz显示黑屏
**解决方案**：
- 检查MID-360数据是否正常发布
- 确认话题名称是否正确
- 重新编译faster_lio

### Q2: 自动重定位失败
**解决方案**：
- 确保机器人在建图区域内
- 尝试移动到特征明显位置
- 使用手动重定位作为备选

### Q3: 角度计算不准确
**解决方案**：
- 检查篮筐标记位置是否准确
- 确认重定位是否成功
- 验证IMU数据是否正常

## 🔄 系统流程图

```
开始
  ↓
阶段一：建图 🗺️
  ├── 启动雷达 + faster-lio建图
  ├── 行走建图
  ├── 移动到篮筐正下方
  ├── 标记篮筐位置
  └── 中断保存地图文件
  ↓
阶段二：导航定位 🧭
  ├── 机器人从建图原点出发
  ├── 启动雷达 + faster-lio数据处理
  ├── 加载保存的地图
  ├── 确定机器人位置（原点）
  ├── 确定篮筐位置（标记点）
  └── 启动篮筐定位计算
  ↓
实时计算 📊
  ├── 获取机器人当前位置
  ├── 计算与篮筐角度 (/y_angle)
  ├── 计算与篮筐距离 (/xy_distance)
  └── 计算X轴差值 (/x_distance)
```

## 🔬 技术原理

### 自动重定位算法
```python
# ICP (Iterative Closest Point) 算法流程
1. 加载预建地图点云
2. 获取当前激光扫描
3. 下采样提高匹配速度
4. 执行点到点ICP匹配
5. 多假设匹配提高成功率
6. 评估匹配质量并发布结果
```

### 角度计算公式
```python
# 计算雷达与篮筐的角度关系
basket_angle = atan2(dy, dx)  # 篮筐方向角
lidar_y_axis_angle = yaw + π/2  # 雷达y轴方向
y_angle = basket_angle - lidar_y_axis_angle  # 夹角
```

### 坐标系说明
- **世界坐标系**: `camera_init` (faster_lio使用)
- **机体坐标系**: 以雷达为原点的坐标系
- **标记坐标**: 篮筐在世界坐标系中的位置

## 📈 性能参数

### 建图性能
- **点云频率**: 10Hz
- **IMU频率**: 200Hz
- **建图精度**: ±5cm (典型环境)
- **最大建图范围**: 200m

### 重定位性能
- **自动重定位成功率**: >90% (特征丰富环境)
- **重定位时间**: 5-15秒
- **位置精度**: ±10cm
- **角度精度**: ±2°

## 🛠️ 配置文件说明

### `mid360.yaml` 关键参数
```yaml
# 预处理参数
lidar_type: 4          # MID-360专用类型
blind: 0.5             # 盲区距离
point_filter_num: 1    # 点云下采样率

# 建图参数
ivox_grid_resolution: 0.1    # 体素网格分辨率
filter_size_surf: 0.1        # 表面滤波器大小
det_range: 200.0             # 最大检测距离
fov_degree: 360              # 水平视场角
```

## 📊 数据格式

### 篮筐标记点格式 (JSON)
```json
[
  {
    "id": 0,
    "x": 1.234,
    "y": 5.678,
    "z": 0.000,
    "timestamp": 1640995200.123,
    "frame_id": "camera_init",
    "description": "标记点_0"
  }
]
```

### 发布话题数据类型
```python
/y_angle: std_msgs/Float64        # 角度值（度）
/xy_distance: std_msgs/Float64    # 距离值（米）
/x_distance: std_msgs/Float64     # X差值（米）
/initialpose: geometry_msgs/PoseWithCovarianceStamped
```

## 🚨 故障排除

### 常见错误及解决方案

#### 1. 编译错误
```bash
# 错误: 找不到livox_ros_driver
sudo apt install ros-noetic-livox-ros-driver

# 错误: PCL版本不兼容
sudo apt install libpcl-dev
```

#### 2. 运行时错误
```bash
# 错误: 话题无数据
rostopic list  # 检查话题是否存在
rostopic hz /livox/lidar  # 检查数据频率

# 错误: TF变换错误
rosrun tf tf_monitor  # 检查TF树
```

#### 3. 重定位失败
```bash
# 检查点云数据
rostopic echo /cloud_registered -n 1

# 检查地图文件
ls -la PCD/scans.pcd

# 安装依赖
pip install open3d
```

## 📋 命令行快速参考

### 建图命令
```bash
# 启动建图
roslaunch faster_lio mapping_mid360.launch rviz:=true

# 标记篮筐
python3 src/faster-lio/scripts/mark_point.py

# 查看建图结果
ls PCD/ marked_points/
```

### 导航命令（按顺序执行）
```bash
# 终端1：启动定位系统
roslaunch faster_lio mapping_mid360.launch rviz:=true

# 终端2：第一步 - 重定位
./src/faster-lio/scripts/start_navigation.sh  # 选择3

# 终端2：第二步 - 篮筐定位（重定位成功后）
./src/faster-lio/scripts/start_navigation.sh  # 选择2
```

### 直接命令（高级用户）
```bash
# 自动重定位
python3 src/faster-lio/scripts/auto_relocalize.py

# 手动重定位
python3 src/faster-lio/scripts/relocalize.py 1.0 2.0 0.0 90

# 篮筐定位
python3 src/faster-lio/scripts/localization_with_basket.py
```

### 监控命令
```bash
# 监控话题
rostopic echo /y_angle
rostopic echo /xy_distance
rostopic hz /livox/lidar

# 查看节点
rosnode list
rosnode info /laserMapping
```

## 📞 技术支持

### 问题诊断步骤
1. **检查硬件连接**
   ```bash
   lsusb  # 检查USB设备
   rostopic list | grep livox  # 检查Livox话题
   ```

2. **检查软件环境**
   ```bash
   rosversion -d  # 检查ROS版本
   python3 -c "import open3d; print('OK')"  # 检查依赖
   ```

3. **检查配置文件**
   ```bash
   rosparam list  # 检查参数加载
   rosparam get /lidar_type  # 检查关键参数
   ```

### 性能优化建议
1. **硬件优化**
   - 使用SSD存储提高I/O性能
   - 增加内存避免大场景建图时内存不足
   - 使用高性能CPU提高实时性

2. **软件优化**
   - 调整点云下采样参数
   - 优化体素网格分辨率
   - 根据场景调整检测范围

3. **环境优化**
   - 选择特征丰富的环境
   - 避免强光直射影响激光雷达
   - 保持稳定的移动速度

---

**版本**: v1.0
**更新日期**: 2024-01-01
**兼容性**: ROS Noetic, Livox MID-360
**作者**: Faster-LIO MID-360 Team
