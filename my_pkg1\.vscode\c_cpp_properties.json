{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/home/<USER>/r2_2/devel/include/**", "/home/<USER>/rtab_ws/devel/include/**", "/home/<USER>/carto_ws/install_isolated/include/**", "/home/<USER>/laser_ws/devel/include/**", "/home/<USER>/realsense_ws/devel/include/**", "/opt/ros/noetic/include/**", "/home/<USER>/catkin/devel/include/**", "/home/<USER>/catkin/src/atr_msg/include/**", "/home/<USER>/catkin_ws/src/VINS-Fusion/camera_models/include/**", "/home/<USER>/catkin/src/imu_pkg/include/**", "/home/<USER>/catkin/src/lider_pkg/include/**", "/home/<USER>/laser_ws/src/lsx10/lslid<PERSON>_driver/include/**", "/home/<USER>/catkin/src/map_pkg/include/**", "/home/<USER>/catkin/src/nav_pkg/include/**", "/home/<USER>/catkin_ws/src/odom/include/**", "/home/<USER>/catkin/src/qq_msgs/include/**", "/home/<USER>/realsense_ws/src/realsense-ros/realsense2_camera/include/**", "/home/<USER>/rtab_ws/src/rtabmap_ros/rtabmap_conversions/include/**", "/home/<USER>/rtab_ws/src/rtabmap_ros/rtabmap_costmap_plugins/include/**", "/home/<USER>/rtab_ws/src/rtabmap_ros/rtabmap_odom/include/**", "/home/<USER>/rtab_ws/src/rtabmap_ros/rtabmap_rviz_plugins/include/**", "/home/<USER>/rtab_ws/src/rtabmap_ros/rtabmap_slam/include/**", "/home/<USER>/rtab_ws/src/rtabmap_ros/rtabmap_sync/include/**", "/home/<USER>/rtab_ws/src/rtabmap_ros/rtabmap_util/include/**", "/home/<USER>/rtab_ws/src/rtabmap_ros/rtabmap_viz/include/**", "/home/<USER>/catkin/src/slam_pkg/include/**", "/home/<USER>/catkin/src/ssr_pag/include/**", "/home/<USER>/catkin/src/vel_pkg/include/**", "/home/<USER>/catkin/src/wpb_home/wpb_home_bringup/include/**", "/home/<USER>/catkin/src/wpb_home/wpb_home_tutorials/include/**", "/home/<USER>/catkin/src/wpb_home/wpbh_local_planner/include/**", "/home/<USER>/catkin/src/wpr_simulation/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}