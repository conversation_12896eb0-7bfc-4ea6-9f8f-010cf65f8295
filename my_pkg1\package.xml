<?xml version="1.0"?>
<package format="2">
  <name>my_pkg1</name>
  <version>0.0.1</version>
  <description>Filter Livox point cloud by height (Python)</description>

  <maintainer email="<EMAIL>">your_name</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>

  <build_depend>rospy</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>pcl_ros</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>python3-scipy</build_depend>
  <build_depend>python3-sklearn</build_depend>
  <exec_depend>python3-scipy</exec_depend>
  <exec_depend>python3-sklearn</exec_depend>

  <exec_depend>rospy</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>pcl_ros</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>visualization_msgs</exec_depend>
  <exec_depend>python3-numpy</exec_depend> 
  <exec_depend>python3-opencv</exec_depend> 
   

</package>

