add_library(${PROJECT_NAME}
        laser_mapping.cc
        pointcloud_preprocess.cc
        options.cc
        utils.cc
        )

add_dependencies(${PROJECT_NAME} ${PROJECT_NAME}_gencpp livox_ros_driver_gencpp livox_ros_driver2_gencpp)
target_link_libraries(${PROJECT_NAME}
        ${catkin_LIBRARIES}
        ${PCL_LIBRARIES}
        ${PYTHON_LIBRARIES}
        tbb
        glog
        yaml-cpp
        )

target_include_directories(${PROJECT_NAME} PRIVATE ${PYTHON_INCLUDE_DIRS})

# 添加odometry_subscriber可执行文件
add_executable(odometry_subscriber odometry_subscriber.cpp)
add_dependencies(odometry_subscriber ${PROJECT_NAME}_gencpp)
target_link_libraries(odometry_subscriber
        ${catkin_LIBRARIES}
        )
