<launch>
<!-- 先启动公共 launch（要优先执行） -->
<include file="$(find livox_ros_driver2)/launch_ROS1/rviz_MID360.launch" />
 
 <!-- 启动 filter_by_height.py -->
 <!-- 观察截取的区域 -->
 <node name="filter_by_height" pkg="my_pkg1" type="filter_by_height.py" output="screen" >
    <param name="min_z" value="17.50" />
    <param name="max_z" value="20.0" />
  </node>

  <!-- 启动 flatten_slice_by_height.py -->
  <!-- 切片 -->
  <node name="flatten_slice_by_height" pkg="my_pkg1" type="flatten_slice_by_height.py" output="screen" >
    <param name="min_z" value="1.75" />
    <param name="max_z" value="1.8" />
  </node>

  <!-- 检测篮筐 -->
  <!-- <node name="detect_hoop_2d" pkg="my_pkg1" type="detect_hoop_2d.py" output="screen" /> -->
  

  <!-- 启动第一个 RViz 配置：recongnize.rviz -->
  <node name="rviz_recongnize" pkg="rviz" type="rviz" args="-d /home/<USER>/test_G/src/my_pkg1/rviz/recongnize.rviz" output="screen" />

  <!-- 启动第二个 RViz 配置：result.rviz -->
  <node name="rviz_result" pkg="rviz" type="rviz" args="-d /home/<USER>/test_G/src/my_pkg1/rviz/result.rviz" output="screen" />

</launch>
