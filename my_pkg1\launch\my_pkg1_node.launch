<launch>
 <!-- 启动 filter_by_height.py -->
 <node name="filter_by_height" pkg="my_pkg1" type="filter_by_height.py" output="screen" >
    <param name="min_z" value="0.75" />
    <param name="max_z" value="10.8" />
  </node>

  <!-- 启动 flatten_slice_by_height.py -->
  <node name="flatten_slice_by_height" pkg="my_pkg1" type="flatten_slice_by_height.py" output="screen" >
    <param name="min_z" value="1.75" />
    <param name="max_z" value="1.8" />
  </node>
</launch>
