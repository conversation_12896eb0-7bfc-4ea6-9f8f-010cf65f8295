#!/usr/bin/env python3
"""
手动发送初始位姿脚本

功能：
1. 发送固定的初始位姿（原点）
2. 支持多种发送模式：
   - 默认：发送一次
   - -n <次数>：循环发送指定次数
   - -l：一直循环发送

使用方法：
python3 send_initial_pose.py           # 发送一次
python3 send_initial_pose.py -n 5      # 发送5次
python3 send_initial_pose.py -l        # 一直发送
"""

import rospy
import argparse
import time
from geometry_msgs.msg import PoseWithCovarianceStamped

class InitialPoseSender:
    def __init__(self, topic_name="/initialpose", frame_id="map"):
        """
        初始化发送器
        
        Args:
            topic_name: 发布话题名称
            frame_id: 坐标系名称
        """
        rospy.init_node('initial_pose_sender', anonymous=True)
        
        # 创建发布器
        self.pub_initial_pose = rospy.Publisher(topic_name, PoseWithCovarianceStamped, queue_size=10)
        
        # 设置固定的初始位姿（原点）
        self.fixed_initial_pose = PoseWithCovarianceStamped()
        self.fixed_initial_pose.header.frame_id = frame_id
        self.fixed_initial_pose.pose.pose.position.x = 0.0
        self.fixed_initial_pose.pose.pose.position.y = 0.0
        self.fixed_initial_pose.pose.pose.position.z = 0.0
        self.fixed_initial_pose.pose.pose.orientation.x = 0.0
        self.fixed_initial_pose.pose.pose.orientation.y = 0.0
        self.fixed_initial_pose.pose.pose.orientation.z = 0.0
        self.fixed_initial_pose.pose.pose.orientation.w = 1.0
        
        # 设置协方差矩阵（表示位姿的不确定性）
        # 6x6矩阵，对应 [x, y, z, roll, pitch, yaw]
        covariance = [0.0] * 36
        # 设置位置不确定性（对角线元素）
        covariance[0] = 0.25   # x方向方差
        covariance[7] = 0.25   # y方向方差
        covariance[14] = 0.0   # z方向方差（2D情况下为0）
        covariance[21] = 0.0   # roll方差
        covariance[28] = 0.0   # pitch方差
        covariance[35] = 0.06853891945200942  # yaw方向方差
        
        self.fixed_initial_pose.pose.covariance = covariance
        
        print(f"✓ 初始位姿发送器已初始化")
        print(f"  - 发布话题: {topic_name}")
        print(f"  - 坐标系: {frame_id}")
        print(f"  - 位姿: 原点 (0, 0, 0) 朝向 (0, 0, 0, 1)")
        
        # 等待发布器连接
        rospy.sleep(0.5)
    
    def send_pose_once(self):
        """发送一次初始位姿"""
        # 更新时间戳
        self.fixed_initial_pose.header.stamp = rospy.Time.now()
        
        # 发布位姿
        self.pub_initial_pose.publish(self.fixed_initial_pose)
        
        print(f"📍 已发送初始位姿 (时间戳: {self.fixed_initial_pose.header.stamp.to_sec():.3f})")
    
    def send_pose_multiple(self, count, interval=1.0):
        """
        发送多次初始位姿
        
        Args:
            count: 发送次数
            interval: 发送间隔（秒）
        """
        print(f"🔄 开始发送 {count} 次初始位姿，间隔 {interval} 秒")
        
        for i in range(count):
            if rospy.is_shutdown():
                print("❌ ROS节点已关闭，停止发送")
                break
                
            self.send_pose_once()
            print(f"  进度: {i+1}/{count}")
            
            # 如果不是最后一次，等待间隔时间
            if i < count - 1:
                time.sleep(interval)
        
        print(f"✅ 完成发送 {min(i+1, count)} 次初始位姿")
    
    def send_pose_loop(self, interval=1.0):
        """
        循环发送初始位姿
        
        Args:
            interval: 发送间隔（秒）
        """
        print(f"🔁 开始循环发送初始位姿，间隔 {interval} 秒")
        print("按 Ctrl+C 停止发送")
        
        count = 0
        try:
            while not rospy.is_shutdown():
                self.send_pose_once()
                count += 1
                print(f"  已发送: {count} 次")
                time.sleep(interval)
        except KeyboardInterrupt:
            print(f"\n⏹️  用户中断，停止发送")
        except Exception as e:
            print(f"❌ 发送过程中出错: {e}")
        
        print(f"✅ 总共发送了 {count} 次初始位姿")

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='手动发送初始位姿脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python3 send_initial_pose.py           # 发送一次
  python3 send_initial_pose.py -n 5      # 发送5次
  python3 send_initial_pose.py -l        # 一直发送
  python3 send_initial_pose.py -n 10 -i 0.5  # 发送10次，间隔0.5秒
        """
    )
    
    # 互斥参数组：-n 和 -l 不能同时使用
    group = parser.add_mutually_exclusive_group()
    group.add_argument('-n', '--number', type=int, metavar='COUNT',
                      help='发送次数（默认发送1次）')
    group.add_argument('-l', '--loop', action='store_true',
                      help='循环发送（按Ctrl+C停止）')
    
    parser.add_argument('-i', '--interval', type=float, default=1.0, metavar='SECONDS',
                       help='发送间隔时间（秒，默认1.0）')
    parser.add_argument('-t', '--topic', type=str, default='/initialpose', metavar='TOPIC',
                       help='发布话题名称（默认/initialpose）')
    parser.add_argument('-f', '--frame', type=str, default='map', metavar='FRAME',
                       help='坐标系名称（默认map）')
    
    return parser.parse_args()

def main():
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        print("=== 初始位姿发送器 ===")
        
        # 创建发送器
        sender = InitialPoseSender(topic_name=args.topic, frame_id=args.frame)
        
        # 根据参数选择发送模式
        if args.loop:
            # 循环发送模式
            sender.send_pose_loop(interval=args.interval)
        elif args.number:
            # 多次发送模式
            if args.number <= 0:
                print("❌ 发送次数必须大于0")
                return
            sender.send_pose_multiple(args.number, interval=args.interval)
        else:
            # 单次发送模式
            sender.send_pose_once()
        
    except rospy.ROSInterruptException:
        print("❌ ROS中断")
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序出错: {e}")

if __name__ == '__main__':
    main()
