# Faster-LIO 重定位功能使用说明

## 功能概述

重定位功能允许Faster-LIO在已知地图中重新定位机器人的位置，适用于以下场景：
- 机器人重启后需要在已知环境中重新定位
- 机器人在SLAM过程中丢失位置需要重新定位
- 在预先建好的地图中进行导航

## 使用步骤

### 1. 准备已知地图
首先需要有一个PCD格式的地图文件，可以通过以下方式获得：
- 使用Faster-LIO建图后保存的PCD文件
- 其他SLAM算法生成的点云地图

### 2. 配置参数

#### 方法1：在launch文件中直接设置参数
```xml
<!-- 重定位相关参数 -->
<param name="relocalization/relocalization_enable" type="bool" value="true" />
<param name="relocalization/known_map_path" type="string" value="/path/to/your/map.pcd" />
<param name="relocalization/icp_threshold" type="double" value="0.1" />
<param name="relocalization/max_icp_iterations" type="int" value="50" />
<param name="relocalization/initial_pose_topic" type="string" value="/initialpose" />
```

#### 方法2：使用参数化launch文件
```bash
# 使用默认参数启动
roslaunch faster_lio mapping_avia_relocalization.launch

# 自定义参数启动
roslaunch faster_lio mapping_avia_relocalization.launch \
    known_map_path:="/home/<USER>/my_map.pcd" \
    icp_threshold:=0.05 \
    max_icp_iterations:=100
```

### 3. 启动系统
```bash
# 启动重定位模式
roslaunch faster_lio mapping_avia_relocalization.launch known_map_path:="/path/to/your/map.pcd"
```

### 4. 在RViz中给出初始位姿
1. 启动RViz：`rviz`
2. 添加以下显示项：
   - `/known_map` (PointCloud2) - 显示已知地图
   - `/cloud_registered` (PointCloud2) - 显示当前激光点云
   - `/path` (Path) - 显示轨迹
   - `/Odometry` (Odometry) - 显示位姿
3. 使用RViz的"2D Pose Estimate"工具在地图上点击并拖拽，给出机器人的大概初始位置和朝向

### 5. 重定位过程
- 系统会自动尝试使用ICP算法进行精确重定位
- 如果重定位成功，系统会切换到正常SLAM模式
- 如果重定位失败，系统会提示重新给出初始位姿

## 参数说明

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `relocalization_enable` | bool | false | 是否启用重定位功能 |
| `known_map_path` | string | "" | 已知地图PCD文件路径 |
| `icp_threshold` | double | 0.1 | ICP配准成功的阈值（越小越严格） |
| `max_icp_iterations` | int | 50 | ICP最大迭代次数 |
| `initial_pose_topic` | string | "/initialpose" | 接收初始位姿的话题名 |

## 故障排除

### 重定位失败
如果看到以下错误信息：
```
ICP relocalization failed. Converged: true, Fitness score: 1.003742
Please provide a new initial pose in RViz using '2D Pose Estimate' tool
```

解决方法：
1. 检查初始位姿是否准确（位置和朝向）
2. 尝试调整`icp_threshold`参数（增大阈值）
3. 确保当前环境与地图匹配
4. 重新在RViz中给出更准确的初始位姿

### 地图加载失败
如果看到：
```
Failed to load known map: /path/to/map.pcd
```

解决方法：
1. 检查地图文件路径是否正确
2. 确保PCD文件存在且可读
3. 检查文件格式是否正确

## 注意事项

1. **地图质量**：确保已知地图质量良好，点云密度适中
2. **初始位姿精度**：初始位姿越准确，重定位成功率越高
3. **环境匹配**：当前环境应与地图环境基本一致
4. **传感器数据**：确保激光雷达和IMU数据正常
5. **计算资源**：重定位过程需要一定的计算资源，特别是大地图时

## 话题说明

### 订阅话题
- `/initialpose` (geometry_msgs/PoseWithCovarianceStamped) - 接收RViz的初始位姿

### 发布话题
- `/known_map` (sensor_msgs/PointCloud2) - 发布已知地图用于可视化
- `/cloud_registered` (sensor_msgs/PointCloud2) - 当前配准后的点云
- `/path` (nav_msgs/Path) - 机器人轨迹
- `/Odometry` (nav_msgs/Odometry) - 机器人位姿

## 示例命令

```bash
# 1. 启动重定位模式
roslaunch faster_lio mapping_avia_relocalization.launch \
    known_map_path:="/home/<USER>/workspace/map.pcd" \
    icp_threshold:=0.1

# 2. 启动RViz（另一个终端）
rviz

# 3. 在RViz中使用"2D Pose Estimate"工具给出初始位姿

# 4. 观察重定位结果
rostopic echo /Odometry
```
