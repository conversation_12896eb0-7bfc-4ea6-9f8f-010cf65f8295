{"configurations": [{"browse": {"databaseFilename": "${default}", "limitSymbolsToIncludedHeaders": false}, "includePath": ["/opt/ros/noetic/include/**", "/home/<USER>/test_G/devel/include/**", "/home/<USER>/lidar_ws/devel/include/**", "/home/<USER>/lidar_ws/src/lidar_distance_measurement/include/**", "/home/<USER>/lidar_ws/src/lidar2/include/**", "/usr/include/**"], "name": "ROS", "intelliSenseMode": "gcc-x64", "compilerPath": "/usr/bin/gcc", "cStandard": "gnu11", "cppStandard": "c++14"}], "version": 4}