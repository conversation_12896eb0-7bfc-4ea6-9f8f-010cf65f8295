common:
  lid_topic: "/livox/lidar"
  imu_topic: "/livox/imu"
  time_sync_en: false         # ONLY turn on when external time synchronization is really not possible

preprocess:
  lidar_type: 1                # 1 for Livox serials LiDAR, 2 for Velodyne LiDAR, 3 for ouster LiDAR,
  scan_line: 4
  blind: 0.5
  time_scale: 1e-3

mapping:
  acc_cov: 0.1
  gyr_cov: 0.1
  b_acc_cov: 0.0001
  b_gyr_cov: 0.0001
  fov_degree: 90
  det_range: 450.0
  extrinsic_est_en: false      # true: enable the online estimation of IMU-LiDAR extrinsic
  extrinsic_T: [ 0.04165, 0.02326, -0.0284 ]
  extrinsic_R: [ 1, 0, 0,
                 0, 1, 0,
                 0, 0, 1 ]

publish:
  path_publish_en: false
  scan_publish_en: true       # false: close all the point cloud output
  scan_effect_pub_en: true    # true: publish the pointscloud of effect point
  dense_publish_en: false       # false: low down the points number in a global-frame point clouds scan.
  scan_bodyframe_pub_en: true  # true: output the point cloud scans in IMU-body-frame

path_save_en: true                 # 保存轨迹，用于精度计算和比较

pcd_save:
  pcd_save_en: true
  interval: -1                 # how many LiDAR frames saved in each pcd file;
  # -1 : all frames will be saved in ONE pcd file, may lead to memory crash when having too much frames.

feature_extract_enable: false
point_filter_num: 3
max_iteration: 3
filter_size_surf: 0.5
filter_size_map: 0.5             # 暂时未用到，代码中为0， 即倾向于将降采样后的scan中的所有点加入map
cube_side_length: 1000

ivox_grid_resolution: 0.5        # default=0.2
ivox_nearby_type: 18             # 6, 18, 26
esti_plane_threshold: 0.1        # default=0.1

# 重定位相关参数
relocalization:
  relocalization_enable: true                  # 是否启用重定位功能
  known_map_path: "/home/<USER>/r2_2/src/faster-lio/PCD/mytest1.pcd"   # 已知地图路径
  icp_threshold: 0.1            # ICP重定位成功阈值
  max_icp_iterations: 50            # ICP最大迭代次数
  initial_pose_topic: "/initialpose"  # rviz2初始位姿话题
