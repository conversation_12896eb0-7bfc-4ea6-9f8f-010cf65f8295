#!/bin/bash

echo "=== Livox LIO 诊断脚本 ==="
echo "时间: $(date)"
echo ""

# 1. 检查网络连接
echo "1. 检查雷达网络连接..."
ping -c 3 ************* > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✓ 雷达网络连接正常 (*************)"
else
    echo "✗ 雷达网络连接失败 (*************)"
    echo "请检查网络配置和雷达IP地址"
fi

# 2. 检查ROS话题
echo ""
echo "2. 检查ROS话题状态..."
if command -v rostopic &> /dev/null; then
    echo "检查激光雷达话题:"
    timeout 5 rostopic hz /livox/lidar 2>/dev/null || echo "✗ /livox/lidar 话题无数据或不存在"
    
    echo "检查IMU话题:"
    timeout 5 rostopic hz /livox/imu 2>/dev/null || echo "✗ /livox/imu 话题无数据或不存在"
else
    echo "ROS环境未正确设置"
fi

# 3. 检查配置文件
echo ""
echo "3. 检查配置文件..."
CONFIG_FILE="../config/MID360_config.json"
if [ -f "$CONFIG_FILE" ]; then
    echo "✓ 配置文件存在: $CONFIG_FILE"
    echo "雷达IP: $(grep -o '"ip" : "[^"]*"' $CONFIG_FILE)"
    echo "主机IP: $(grep -o '"cmd_data_ip" : "[^"]*"' $CONFIG_FILE)"
else
    echo "✗ 配置文件不存在: $CONFIG_FILE"
fi

# 4. 检查进程
echo ""
echo "4. 检查相关进程..."
if pgrep -f "livox_ros_driver2_node" > /dev/null; then
    echo "✓ livox_ros_driver2_node 进程运行中"
else
    echo "✗ livox_ros_driver2_node 进程未运行"
fi

# 5. 系统资源检查
echo ""
echo "5. 系统资源检查..."
echo "内存使用率: $(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')"
echo "CPU负载: $(uptime | awk -F'load average:' '{print $2}')"

echo ""
echo "=== 诊断完成 ==="
echo ""
echo "常见解决方案:"
echo "1. 如果网络连接失败，检查网线和IP配置"
echo "2. 如果话题无数据，重启驱动节点"
echo "3. 如果LIO残差过高，尝试重启整个系统"
echo "4. 检查雷达是否需要重新标定"
