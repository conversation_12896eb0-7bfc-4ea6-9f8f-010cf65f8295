 #!/usr/bin/env python
import rospy
from std_msgs.msg import Float64
import math

class FittingCurveSolver:
    def __init__(self):
        rospy.init_node('fitting_curve_solver')
        
        # 拟合曲线方程系数: 2x^3 + 5x^2 + 3x + 4
        self.coefficients = [4, 3, 5, 2]  # [常数项, 一次项系数, 二次项系数, 三次项系数]
        
        rospy.loginfo("拟合曲线求解器已初始化")
        rospy.loginfo("拟合曲线方程: 2x^3 + 5x^2 + 3x + 4")
        
        # 订阅 /xy_distance 话题
        rospy.Subscriber("/xy_distance", Float64, self.callback)

        # 发布 /fire_angle 话题
        self.fire_angle_pub = rospy.Publisher('/fire_angle', Float64, queue_size=10)
        rate = rospy.Rate(1)
        
    def calculate_fire_angle(self, xy_distance):
        """
        使用拟合曲线方程计算fire_angle
        方程: 7776x^5 - 1.322e+05x^4 + 8.977e+05x^3 - 3.047e+06x^2 + 5.166e+06x - 3.501e+06
        Args:
            xy_distance: 输入的自变量x
        Returns:
            fire_angle: 计算得到的角度值
        """
       
        x = xy_distance
        if x < 3.546:
            fire_angle = (
            -20.03 * x**2
            + 108 * x
            - 79.04
        )
        elif 3.546 <= x < 3.938:
        # 这里用你原来的拟合曲线，比如：
             fire_angle = (
                12.1 * x**2
                - 103 * x
                + 275.8
            )
        else :
            fire_angle =18.71 * x**2 - 166.8  * x + 425.6

        return fire_angle
    
    def callback(self, msg):
        xy_distance = msg.data
        rospy.loginfo("收到xy_distance数据: %.6f", xy_distance)

        fire_angle = self.calculate_fire_angle(xy_distance)
    
        fire_angle_msg = Float64()
        fire_angle_msg.data = fire_angle
        self.fire_angle_pub.publish(fire_angle_msg)

        print("=" * 50)
        print("输入xy_distance: {:.6f}".format(xy_distance))
        print("计算得到的fire_angle: {:.6f}".format(fire_angle))
        print("已发布到 /fire_angle 话题")
        print("=" * 50)
    
    def run(self):
        rospy.loginfo("拟合曲线求解器已启动，等待 /xy_distance 话题数据...")
        rospy.spin()

if __name__ == '__main__':
    try:
        solver = FittingCurveSolver()
        solver.run()
    except rospy.ROSInterruptException:
        pass