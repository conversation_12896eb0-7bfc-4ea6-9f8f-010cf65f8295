#!/usr/bin/env python3
"""
篮筐角度距离计算器
只负责计算，不加载地图

功能：
1. 订阅机器人位置
2. 加载篮筐标记点
3. 计算角度和距离
4. 发布计算结果

使用方法：
python3 basket_calculator.py
"""

import rospy
import json
import os
import math
from nav_msgs.msg import Odometry
from std_msgs.msg import Float64
from tf.transformations import euler_from_quaternion

class BasketCalculator:
    def __init__(self):
        rospy.init_node('basket_calculator', anonymous=True)
        # 用于存储第一次计算出的角度偏差，实现“首次计算即校准”
        self.angle_offset = None

        # 手动角度偏移量（通过ROS参数设置）
        self.manual_angle_offset_param = '/basket_calculator/manual_angle_offset'
        rospy.set_param(self.manual_angle_offset_param, 0.0)  # 初始化为0度

        # 当前位置和姿态
        self.current_pose = None
        self.current_orientation = None
        self.basket_points = []
        self.selected_basket_id = 0

        # 雷达在机器人坐标系中的位置 (x, y, z)
        self.lidar_offset_x = 0.0
        self.lidar_offset_y = -0
        self.lidar_offset_z = 0
        
        
        # 角度和距离发布器
        self.y_angle_pub = rospy.Publisher('/y_angle', Float64, queue_size=10)
        self.xy_distance_pub = rospy.Publisher('/xy_distance', Float64, queue_size=10)
        self.x_distance_pub = rospy.Publisher('/x_distance', Float64, queue_size=10)
        
        # ROS订阅器
        self.odom_sub = rospy.Subscriber('/Odometry', Odometry, self.odom_callback)
        
        print("=== 篮筐角度距离计算器 ===")
        print("功能：只计算角度距离，不加载地图")
        print("坐标变换：雷达坐标系 -> 机器人坐标系")
        
      

    def odom_callback(self, msg):
        """处理里程计数据"""
        self.current_pose = msg.pose.pose.position
        self.current_orientation = msg.pose.pose.orientation
        
        if self.basket_points and self.selected_basket_id < len(self.basket_points):    
            self.calculate_basket_relationship()


   


    def calculate_basket_relationship(self):
        """计算雷达与篮筐的角度和距离关系"""
        if self.current_pose is None or not self.basket_points:
            return
        
        # 获取目标篮筐位置
        basket = self.basket_points[self.selected_basket_id]
        basket_x = basket['x']
        basket_y = basket['y']
        basket_z = basket['z']
        
        # 当前雷达位置
        lidar_x = self.current_pose.x
        lidar_y = self.current_pose.y
        lidar_z = self.current_pose.z
        
        # 计算雷达坐标系下的差值（保持原有的x_distance和xy_distance计算）
        dx_lidar = basket_x - lidar_x
        dy_lidar = basket_y - lidar_y
        dz_lidar = basket_z - lidar_z

        # 雷达坐标系下的2D投影距离（保持不变）
        xy_distance = math.sqrt(dx_lidar*dx_lidar + dy_lidar*dy_lidar)




        # 计算机器人朝向与篮筐连线的夹角差值（机器人坐标系）
        # 1. 获取机器人当前朝向（yaw角）
        orientation_q = self.current_orientation
        orientation_list = [orientation_q.x, orientation_q.y, orientation_q.z, orientation_q.w]
        (roll, pitch, yaw) = euler_from_quaternion(orientation_list)

        # 2. 计算机器人到篮筐的方向角（机器人坐标系）
        # basket_angle = math.atan2(dy_lidar, dx_lidar)
        basket_angle =- math.atan2(dx_lidar, dy_lidar)

        # 3. 计算机器人朝向与篮筐连线的夹角差值
        raw_y_angle = (basket_angle - yaw)
        # 4. 首次运行时，使用第一次的夹角作为校准偏差
        # 1.22:常数误差         -1.5：以1.5为基准，初始yaw偏差
        if self.angle_offset is None:
            self.angle_offset = raw_y_angle-math.radians(-1.4) +math.radians(1.22) 
            print(f"✓ 首次角度校准完成。基准角度: {math.degrees(raw_y_angle):.1f}° -> 0°")

        # 5. 应用校准和手动偏移，得到最终角度
        manual_offset_deg = rospy.get_param(self.manual_angle_offset_param, 0.0)
        manual_offset_rad = math.radians(manual_offset_deg)
        y_angle = raw_y_angle + self.angle_offset + manual_offset_rad


        # 归一化到[-π, π]
        while y_angle > math.pi:
            y_angle -= 2*math.pi
        while y_angle < -math.pi:
            y_angle += 2*math.pi
        
        # 发布数据
        y_angle_msg = Float64()
        # 往右偏转，-
        y_angle_msg.data = math.degrees(y_angle)   # 转换为度数+1
        self.y_angle_pub.publish(y_angle_msg)
        
        xy_distance_msg = Float64()
        xy_distance_msg.data = xy_distance
        self.xy_distance_pub.publish(xy_distance_msg)
        
        x_distance_msg = Float64()
        x_distance_msg.data = dx_lidar  # 使用雷达坐标系的x差值
        self.x_distance_pub.publish(x_distance_msg)
        
        # 打印调试信息（每秒一次）
        if hasattr(self, 'last_print_time'):
            if rospy.Time.now().to_sec() - self.last_print_time > 1.0:
                manual_offset_display = rospy.get_param(self.manual_angle_offset_param, 0.0)
                print(f"目标篮筐_{self.selected_basket_id}: "
                      f"连线斜率={math.degrees(basket_angle):.2f}°, "
                      f"angle_offset={math.degrees(self.angle_offset):.2f}°, "
                      f"手动偏移={manual_offset_display:.2f}°, "
                      f"yaw值={math.degrees(yaw):.2f}°,"
                      f"距离={xy_distance:.2f}m, y_angle值={y_angle_msg.data:.2f}°, ")
                self.last_print_time = rospy.Time.now().to_sec()
        else:
            self.last_print_time = rospy.Time.now().to_sec()
    
    def load_basket_points(self, points_file):
        """加载篮筐标记点"""
        try:
            with open(points_file, 'r', encoding='utf-8') as f:
                points = json.load(f)
            
            print(f"✓ 加载了 {len(points)} 个篮筐标记点")
            return points
        except Exception as e:
            print(f"❌ 加载篮筐标记点失败: {e}")
            return []
    
    def select_basket(self):
        """选择目标篮筐"""
        if not self.basket_points:
            print("❌ 没有可用的篮筐")
            return
        
        print(f"\n=== 选择目标篮筐 ===")
        print("可用的篮筐:")
        for i, point in enumerate(self.basket_points):
            desc = point.get('description', f'篮筐_{i}')
            print(f"  {i}: {desc} ({point['x']:.3f}, {point['y']:.3f}, {point['z']:.3f})")
        
        try:
            choice = input(f"请选择目标篮筐ID (0-{len(self.basket_points)-1}, 默认0): ").strip()
            if choice == '':
                self.selected_basket_id = 0
            else:
                basket_id = int(choice)
                if 0 <= basket_id < len(self.basket_points):
                    self.selected_basket_id = basket_id
                else:
                    print(f"❌ 无效的篮筐ID，使用默认篮筐0")
                    self.selected_basket_id = 0
        except ValueError:
            print("❌ 输入格式错误，使用默认篮筐0")
            self.selected_basket_id = 0
        
        basket = self.basket_points[self.selected_basket_id]
        desc = basket.get('description', f'篮筐_{self.selected_basket_id}')
        print(f"✓ 选择了篮筐: {desc}")


    
    def run(self):
        """运行计算器"""
        # 查找最新的标记点文件
        points_dir = os.path.expanduser("~/r2_2/src/faster-lio/marked_points")
        if not os.path.exists(points_dir):
            print(f"❌ 标记点目录不存在: {points_dir}")
            return
        
        json_files = [f for f in os.listdir(points_dir) if f.endswith('.json')]
        if not json_files:
            print("❌ 没有找到标记点文件")
            return
        
        json_files.sort(reverse=True)  # 按时间排序，最新的在前
        points_file = os.path.join(points_dir, json_files[0])
        print(f"使用标记点文件: {points_file}")
        
        # 加载篮筐标记点
        self.basket_points = self.load_basket_points(points_file)
        if not self.basket_points:
            return
        
        # 选择目标篮筐
        self.select_basket()
        
        print(f"\n✓ 篮筐计算器已启动")
        print("发布的话题:")
        print("  - /y_angle (Float64) - 机器人朝向与篮筐连线夹角差(度)")
        print("  - /xy_distance (Float64) - 与篮筐2D投影距离(米)")
        print("  - /x_distance (Float64) - 与篮筐x轴差值(米)")
        print("\n坐标系说明:")
        print("  - 角度计算: 机器人当前朝向 vs 机器人到篮筐连线")
        print("  - 雷达坐标系 = 机器人坐标系顺时针旋转90°")
        print("  - 角度: 0°=正对篮筐, 正值=需要左转, 负值=需要右转")
        print("  - 距离: 使用雷达坐标系计算（保持原有逻辑）")
        print("\n等待机器人位置数据...")
        print(f"\n📝 使用说明:")
        print(f"  - 可通过ROS参数动态调整角度偏移:")
        print(f"    rosparam set {self.manual_angle_offset_param} <角度值>")
        print(f"  - 查看当前偏移: rosparam get {self.manual_angle_offset_param}")
        print(f"  - 重置偏移: rosparam set {self.manual_angle_offset_param} 0")

        # 保持运行
        rospy.spin()

def main():
    try:
        calculator = BasketCalculator()
        calculator.run()
    except rospy.ROSInterruptException:
        pass
    except KeyboardInterrupt:
        print("\n👋 篮筐计算器关闭")

if __name__ == '__main__':
    main()
